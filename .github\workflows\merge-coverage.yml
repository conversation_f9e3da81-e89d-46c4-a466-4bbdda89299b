name: Merge coverage and update report

on:
  workflow_dispatch:

jobs:
  Linux:
    runs-on: ubuntu-24.04

    steps:
      - name: Clone main repository
        uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Install dependencies
        run: |
          go fmt ./...
          go vet ./...
          go get ./...
          go mod tidy
          go mod verify
          go build -v ./...

      - name: Start docker container for test in Ubuntu
        run: |
          docker run -d --name pinguem -p 8085:8085 -p 3005:3005 lifailon/pinguem:latest
          docker run -d --name TorAPI -p 8443:8443 lifailon/torapi:latest

      - name: Create pcap files for Linux
        run: |
          sudo tcpdump -i any -c 1 -w test.pcap
          gzip -c test.pcap > test.pcap.gz
          sudo tcpdump -i any -c 1 -w test.pcapng
          gzip -c test.pcapng > test.pcapng.gz
          ls -lh
        continue-on-error: true

      - name: Install tailspin
        run: sudo apt install -y tailspin

      - name: Run unit test in Linux
        run: sudo go test -v -cover -coverprofile linux-coverage.out
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report
        run: |
          mv test-report.md test-linux-report.md
          cat test-linux-report.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true

      - name: Upload test summary report
        uses: actions/upload-artifact@v4
        with:
          name: linux-report
          path: test-linux-report.md

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: linux-coverage
          path: linux-coverage.out

  macOS:
    runs-on: ubuntu-24.04

    steps:
      - name: Clone main repository
        uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Install dependencies
        run: |
          go fmt ./...
          go vet ./...
          go get ./...
          go mod tidy
          go mod verify
          go build -v ./...

      - name: Run unit test in macOS
        run: sudo go test -v --run "TestUnixFiles|TestFlags|TestMainInterface" -cover -coverprofile macos-coverage.out
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report
        run: |
          mv test-report.md test-macos-report.md
          cat test-macos-report.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true

      - name: Upload test summary report
        uses: actions/upload-artifact@v4
        with:
          name: macos-report
          path: test-macos-report.md

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: macos-coverage
          path: macos-coverage.out

  Windows:
    runs-on: windows-2022
    steps:
      - name: Clone main repository
        uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Install dependencies
        run: |
          go fmt ./...
          go vet ./...
          go get ./...
          go mod tidy
          go mod verify
          go build -v ./...

      - name: Create log file for Windows
        run: |
          New-Item -Path "$env:APPDATA\test" -Type Directory
          "line test" | Out-File -FilePath "$env:APPDATA\test\test.log"
        shell: pwsh
        continue-on-error: true

      - name: Run unit test in Windows
        run: go test -v --run "TestWin.*|TestFlags|TestMainInterface|TestMockInterface" -cover -coverprofile windows-coverage.out
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report
        run: |
          Rename-Item -Path test-report.md -NewName test-windows-report.md
          Get-Content test-windows-report.md | Out-File -Append -FilePath $env:GITHUB_STEP_SUMMARY
        shell: pwsh
        continue-on-error: true

      - name: Upload test summary report
        uses: actions/upload-artifact@v4
        with:
          name: windows-report
          path: test-windows-report.md

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: windows-coverage
          path: windows-coverage.out

  merge_coverage_test:
    runs-on: ubuntu-latest
    needs: [Linux, macOS, Windows]
    steps:
      - name: Clone main repository
        uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Download coverage report for Linux
        uses: actions/download-artifact@v4
        with:
          name: linux-coverage
          path: .

      - name: Download coverage report for macOS
        uses: actions/download-artifact@v4
        with:
          name: macos-coverage
          path: .

      - name: Download coverage report for Windows
        uses: actions/download-artifact@v4
        with:
          name: windows-coverage
          path: .

      - name: Check coverage report for Linux
        run: go tool cover -func linux-coverage.out

      - name: Check coverage report for macOS
        run: go tool cover -func macos-coverage.out

      - name: Check coverage report for Windows
        run: go tool cover -func windows-coverage.out

      - name: Install go coverage merge
        run: |
          go install github.com/wadey/gocovmerge@latest
          echo "$HOME/go/bin" >> $GITHUB_PATH
          export PATH=$HOME/go/bin:$PATH
          which gocovmerge

      - name: Merge coverage reports
        run: |
          gocovmerge linux-coverage.out macos-coverage.out windows-coverage.out > merge-coverage.out
          go tool cover -func merge-coverage.out

      - name: Upload merge coverage report
        uses: actions/upload-artifact@v4
        with:
          name: merge-coverage
          path: merge-coverage.out

      - name: Update merge coverage report in Wiki
        uses: ncruces/go-coverage-report@v0
        with:
          coverage-file: merge-coverage.out
          report: true
          chart: true
          amend: true
        continue-on-error: true

      - name: Download test summary report for Linux
        uses: actions/download-artifact@v4
        with:
          name: linux-report
          path: .

      - name: Download test summary report for macOS
        uses: actions/download-artifact@v4
        with:
          name: macos-report
          path: .

      - name: Download test summary report for Windows
        uses: actions/download-artifact@v4
        with:
          name: windows-report
          path: .

      - name: Merge test summary reports
        run: |
          cat "test-linux-report.md" > test-summary-report.md
          cat "test-macos-report.md" >> test-summary-report.md
          cat "test-windows-report.md" >> test-summary-report.md

      - name: Upload merge test summary report
        uses: actions/upload-artifact@v4
        with:
          name: test-summary-report
          path: test-summary-report.md