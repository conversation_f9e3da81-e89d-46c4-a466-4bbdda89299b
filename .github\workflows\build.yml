name: CI

on:
  workflow_dispatch:
    inputs:
      Distro:
        description: 'Select runner image' # https://github.com/actions/runner-images
        required: true
        default: 'ubuntu-20.04'
        type: choice
        options:
          - 'ubuntu-20.04'
          - 'ubuntu-22.04'
          - 'ubuntu-24.04'
          - 'macos-15'
          - 'windows-2022'
      Update:
        description: 'Update dependencies'
        default: false
        type: boolean
      Linter:
        description: '<PERSON><PERSON> checks'
        default: false
        type: boolean
      Test:
        description: 'Unit testing'
        default: false
        type: boolean
      Binary:
        description: 'Build binary'
        default: false
        type: boolean

jobs:
  build:
    runs-on: ${{ github.event.inputs.Distro }}

    steps:
      - name: Clone main repository
        uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Install dependencies
        run: |
          go fmt ./...
          go vet ./...
          go get ./...
          go mod tidy
          go mod verify
          go build -v ./...

      - name: Update dependencies
        if: ${{ github.event.inputs.Update == 'true' }}
        run: go get -u ./...

      - name: <PERSON><PERSON><PERSON> linter check
        if: ${{ github.event.inputs.Linter == 'true' }}
        run: |
          go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
          golangci-lint run ./main.go

      - name: Critic linter check
        if: ${{ github.event.inputs.Linter == 'true' }}
        run: |
          go install github.com/go-critic/go-critic/cmd/gocritic@latest
          gocritic check -enableAll ./main.go

      - name: Security linter check
        if: ${{ github.event.inputs.Linter == 'true' }}
        run: |
          go install github.com/securego/gosec/v2/cmd/gosec@latest
          gosec -severity=high ./...

      # Linux
      - name: Start docker container for test in Ubuntu
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro != 'macos-15' && github.event.inputs.Distro != 'windows-2022' }}
        run: |
          docker run -d --name pinguem -p 8085:8085 -p 3005:3005 lifailon/pinguem:latest
          docker run -d --name TorAPI -p 8443:8443 lifailon/torapi:latest

      - name: Create pcap files for Linux
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro != 'macos-15' && github.event.inputs.Distro != 'windows-2022' }}
        run: |
          sudo tcpdump -i any -c 1 -w test.pcap
          gzip -c test.pcap > test.pcap.gz
          sudo tcpdump -i any -c 1 -w test.pcapng
          gzip -c test.pcapng > test.pcapng.gz
          ls -lh

      - name: Install tailspin
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro != 'macos-15' && github.event.inputs.Distro != 'windows-2022' }}
        run: sudo apt install -y tailspin

      - name: Unit testing (all functions and mock interface) in Linux
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro != 'macos-15' && github.event.inputs.Distro != 'windows-2022' }}
        run: sudo go test -v -cover
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report in Linux
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro != 'macos-15' && github.event.inputs.Distro != 'windows-2022' }}
        run: cat test-report.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true

      # macOS
      - name: Unit testing (files, flags and run main interface) in macOS
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro == 'macos-15' }}
        run: sudo go test -v --run "TestUnixFiles|TestFlags|TestMainInterface" -cover
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report in macOS
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro == 'macos-15' }}
        run: cat test-report.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true

      # Windows
      - name: Create log file for Windows
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro == 'windows-2022' }}
        run: |
          New-Item -Path "$env:APPDATA\test" -Type Directory
          "line test" | Out-File -FilePath "$env:APPDATA\test\test.log"
        shell: pwsh
        continue-on-error: true

      - name: Unit testing (events, files, flags and interface) in Windows
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro == 'windows-2022' }}
        run: go test -v --run "TestWin.*|TestFlags|TestMainInterface|TestMockInterface" -cover
        continue-on-error: true
        timeout-minutes: 5

      - name: Create markdown report in Windows
        if: ${{ github.event.inputs.Test == 'true' && github.event.inputs.Distro == 'windows-2022' }}
        run: Get-Content test-report.md | Out-File -Append -FilePath $env:GITHUB_STEP_SUMMARY
        shell: pwsh
        continue-on-error: true

      # Build
      - name: Build binaries
        if: ${{ github.event.inputs.Binary == 'true' }}
        run: |
          version=$(go run main.go -v)
          echo "Get version from main.go: $version"
          mkdir -p bin
          architectures=("amd64" "arm64")
          for arch in "${architectures[@]}"; do
              CGO_ENABLED=0 GOOS=linux GOARCH=$arch go build -o bin/lazyjournal-$version-linux-$arch
              CGO_ENABLED=0 GOOS=darwin GOARCH=$arch go build -o bin/lazyjournal-$version-darwin-$arch
              CGO_ENABLED=0 GOOS=openbsd GOARCH=$arch go build -o bin/lazyjournal-$version-openbsd-$arch
              CGO_ENABLED=0 GOOS=freebsd GOARCH=$arch go build -o bin/lazyjournal-$version-freebsd-$arch
              CGO_ENABLED=0 GOOS=windows GOARCH=$arch go build -o bin/lazyjournal-$version-windows-$arch.exe
          done
          ls -lh bin
          echo "ARTIFACT_NAME=lazyjournal-$(date +'%d.%m.%Y')" >> $GITHUB_ENV

      - name: Upload binaries
        if: ${{ github.event.inputs.Binary == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: bin/
