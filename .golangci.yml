# https://golangci-lint.run/usage/linters
linters:
  # disable-all: true
  # enable:
  #   - errcheck
  #   - gosimple
  #   - govet
  #   - ineffassign
  #   - staticcheck
  #   - unused
  #   - err113
  #   - unparam
  #   - gocritic
  #   - misspell
  #   - perfsprint
  enable-all: true
  disable:
    - gosec
    - makezero
    - dupl
    - wsl
    - goconst
    - gocognit
    - gocyclo
    - revive
    - maintidx
    - cyclop
    - depguard
    - forbidigo
    - funlen
    - godot
    - gofumpt
    - lll
    - mnd
    - nestif
    - nilerr
    - nlreturn
    - tagliatelle
    - varnamelen
    - wrapcheck
    - ireturn
    - nonamedreturns
    - stylecheck
    - exhaustruct
    - unconvert
    - copyloopvar
    - intrange
    - tenv
    - paralleltest
    - gochecknoglobals
    - prealloc