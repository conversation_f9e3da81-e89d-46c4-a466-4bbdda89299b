env:
  GIT_URL: https://github.com/Lifailon/lazyjournal
  GO_PATH: /usr/local/bin/go

networks:
  bsd:
    hosts:
      - root@*************:22
      - root@*************:22

commands:
  install:
    desc: Install Go
    run: |
      if [ -e "$GO_PATH" ]; then
        $GO_PATH version
      else
        if [ "$(uname -s)" = "FreeBSD" ]; then 
          pkg update
          pkg install -y go
        elif [ "$(uname -s)" = "OpenBSD" ]; then
          pkg_add -u
          yes | pkg_add go
        fi
      fi

  bin:
    desc: Install binary
    run: curl -sS https://raw.githubusercontent.com/Lifailon/lazyjournal/main/install.sh | bash

  run:
    desc: Run interface from release
    run: |
      if [ "$(uname -s)" = "FreeBSD" ]; then 
        . /root/.shrc
      elif [ "$(uname -s)" = "OpenBSD" ]; then
        . /root/.kshrc
      fi
      lazyjournal -v
      lazyjournal -a
      tmux new-session -d -s test-session "lazyjournal"
      sleep 1
      tmux capture-pane -p
      tmux kill-session -t test-session

  clone:
    desc: Clone repository
    run: |
      rm -rf lazyjournal
      git clone $GIT_URL

  creat:
    desc: Creat directory for upload files
    run: |
      rm -rf lazyjournal
      mkdir lazyjournal

  copy:
    desc: Upload all files to remote hosts
    upload:
      - src: \*
        dst: lazyjournal

  check:
    desc: Check directory
    run: ls -lh lazyjournal

  prep:
    desc: Install dependencies and code preparation
    run: |
      cd lazyjournal
      $GO_PATH fmt ./...
      $GO_PATH vet ./...
      $GO_PATH get ./...
      $GO_PATH mod tidy
      $GO_PATH mod verify
      $GO_PATH build -o /dev/null -v ./...

  test:
    desc: Run unit tests
    run: |
      cd lazyjournal
      $GO_PATH test -v -cover --run "TestUnixFiles|TestColor|TestFilter|TestFlags|TestMainInterface|TestMockInterface"

  remove:
    desc: Remove files on remote hosts
    run: |
      rm -rf lazyjournal
      ls -l lazyjournal 2> /dev/null || echo "lazyjournal removed"

targets:
  release:
    - bin
    - run
  testing:
    - install
    - creat
    - copy
    - check
    - prep
    - test
    - remove