version: '3'

tasks:
  prep:
    desc: "Install dependencies and code preparation"
    cmds:
      - go fmt ./...                    # Formatting code
      - go vet ./...                    # Analyzing code for errors
      - go get ./...                    # Download all dependencies from go.mod
      - go mod tidy                     # Removal of unused and installing missing dependencies
      - go mod verify                   # Checking dependencies
      - go build -o /dev/null -v ./...  # Checking code compilation
  
  update:
    desc: "Update dependencies"
    cmds:
      - task: prep
      - go get -u ./...

  install-lint:
    desc: "Install dependencies for linter checks"
    cmds:
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - go install github.com/go-critic/go-critic/cmd/gocritic@latest
      - go install github.com/securego/gosec/v2/cmd/gosec@latest

  lint:
    desc: "Run linter checks"
    cmds:
      - task: prep
      - task: install-lint
      - golangci-lint run ./main.go
      - gocritic check -enableAll ./main.go
      - gosec -severity=high ./...

  list:
    desc: "Get test list"
    cmds:
      - go test -list . ./...
      - silent: true
        cmd: |
          echo To run the test use: task test -- TestMain

  test:
    desc: "Run the selected unit test"
    cmds:
      - task: prep
      - go test -v -cover --run {{.CLI_ARGS}} ./...

  build:
    desc: "Build"
    vars:
      VERSION:
        sh: go run main.go -v
    cmds:
      - echo Build version {{.VERSION}}
      - task: prep
      - CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bin/lazyjournal-{{.VERSION}}-linux-amd64
      - CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/lazyjournal-{{.VERSION}}-linux-arm64
      - CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -o bin/lazyjournal-{{.VERSION}}-darwin-amd64
      - CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 go build -o bin/lazyjournal-{{.VERSION}}-darwin-arm64
      - CGO_ENABLED=0 GOOS=openbsd GOARCH=amd64 go build -o bin/lazyjournal-{{.VERSION}}-openbsd-amd64
      - CGO_ENABLED=0 GOOS=openbsd GOARCH=arm64 go build -o bin/lazyjournal-{{.VERSION}}-openbsd-arm64
      - CGO_ENABLED=0 GOOS=freebsd GOARCH=amd64 go build -o bin/lazyjournal-{{.VERSION}}-freebsd-amd64
      - CGO_ENABLED=0 GOOS=freebsd GOARCH=arm64 go build -o bin/lazyjournal-{{.VERSION}}-freebsd-arm64
      - CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -o bin/lazyjournal-{{.VERSION}}-windows-amd64
      - CGO_ENABLED=0 GOOS=windows GOARCH=arm64 go build -o bin/lazyjournal-{{.VERSION}}-windows-arm64